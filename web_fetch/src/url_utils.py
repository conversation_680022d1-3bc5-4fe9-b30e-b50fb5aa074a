"""
URL validation and analysis utilities.

This module provides utility functions for URL validation, normalization,
and analysis, as well as response header analysis and content type detection.
"""

from __future__ import annotations

from typing import Dict, Optional

from ..models.http import URLAnalysis, HeaderAnalysis


def is_valid_url(url: str) -> bool:
    """
    Check if a URL is valid.

    Validates URL format, scheme, and basic structure. Supports HTTP, HTTPS,
    FTP, and other common schemes. Does not perform network connectivity checks.

    Args:
        url: URL string to validate. Can be absolute or relative URL.

    Returns:
        True if URL has valid format and scheme, False otherwise.

    Example:
        ```python
        is_valid_url("https://example.com")  # True
        is_valid_url("http://localhost:8080/path")  # True
        is_valid_url("not-a-url")  # False
        is_valid_url("ftp://files.example.com/file.txt")  # True
        ```
    """
    from ..utils import URLValidator
    return URLValidator.is_valid_url(url)


def normalize_url(url: str, base_url: Optional[str] = None) -> str:
    """
    Normalize a URL by resolving relative paths and cleaning up.

    Performs URL normalization including scheme lowercasing, host lowercasing,
    path normalization (removing . and .. segments), query parameter sorting,
    and relative URL resolution against a base URL.

    Args:
        url: URL to normalize. Can be absolute or relative.
        base_url: Optional base URL for resolving relative URLs. If provided
                 and url is relative, the result will be an absolute URL.

    Returns:
        Normalized URL string with consistent formatting.

    Raises:
        ValueError: If url is invalid or base_url is provided but invalid.

    Example:
        ```python
        normalize_url("HTTPS://EXAMPLE.COM/Path/../Other?b=2&a=1")
        # Returns: "https://example.com/Other?a=1&b=2"

        normalize_url("../other", "https://example.com/path/")
        # Returns: "https://example.com/other"
        ```
    """
    from urllib.parse import urljoin
    from ..utils import URLValidator

    # Resolve relative URL against base_url if provided
    if base_url:
        url = urljoin(base_url, url)
    return URLValidator.normalize_url(url)


def analyze_url(url: str) -> URLAnalysis:
    """
    Perform comprehensive URL analysis.

    Args:
        url: URL to analyze

    Returns:
        URLAnalysis object with detailed information
    """
    from ..utils import URLValidator
    return URLValidator.analyze_url(url)


def analyze_headers(headers: Dict[str, str]) -> HeaderAnalysis:
    """
    Analyze HTTP response headers.

    Args:
        headers: Dictionary of response headers

    Returns:
        HeaderAnalysis object with parsed header information
    """
    from ..utils import ResponseAnalyzer
    return ResponseAnalyzer.analyze_headers(headers)


def detect_content_type(headers: Dict[str, str], content: bytes) -> str:
    """
    Detect content type from headers and content.

    Args:
        headers: Response headers
        content: Response content bytes

    Returns:
        Detected content type string
    """
    from ..utils import ResponseAnalyzer
    return ResponseAnalyzer.detect_content_type(headers, content)
